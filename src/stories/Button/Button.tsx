import { useEffect, useRef, useState } from "react";
import { animate, createScope, Scope } from "animejs";
import "./button.scss";
import DownloadIcon from "../../icons/DownloadIcon";

export interface ButtonProps {
  /** Is this the principal call to action on the page? */
  primary?: boolean;
  /** What background color to use */
  backgroundColor?: string;
  /** How large should the button be? */
  size?: "small" | "medium" | "large";
  /** Button contents */
  label: string;
  /** Optional click handler */
  onClick?: () => void;
}

export const Button = ({ primary = false, size = "medium", backgroundColor, label, onClick, ...props }: ButtonProps) => {
  const buttonRef = useRef(null);
  const textRef = useRef(null);
  const iconRef = useRef(null);
  const scope = useRef<Scope>(null);

  const originalStyles = useRef({
    width: "",
    height: "",
    borderRadius: "",
    backgroundColor: "",
    border: "",
    transform: "",
  });

  const originalBackgroundColor = useRef<string>("");

  const [isTransformed, setIsTransformed] = useState(false);

  const mode = primary ? "storybook-button--primary" : "storybook-button--secondary";

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const rotateAnimation = useRef<any>(undefined);

  useEffect(() => {
    if (buttonRef.current) {
      const button = buttonRef.current as HTMLElement;
      const computedStyle = window.getComputedStyle(button);

      // Store the original background color from props or computed style
      originalBackgroundColor.current = backgroundColor || computedStyle.backgroundColor;

      originalStyles.current = {
        width: computedStyle.width,
        height: computedStyle.height,
        borderRadius: computedStyle.borderRadius,
        backgroundColor: originalBackgroundColor.current,
        border: computedStyle.border,
        transform: computedStyle.transform,
      };

      scope.current = createScope({ root: buttonRef.current }).add((self) => {
        if (self) {
          self.add("rotateButton", () => {
            if (buttonRef.current) {
              rotateAnimation.current = animate(buttonRef.current, {
                rotate: "360deg",
                loop: true,
                direction: "normal",
                ease: "linear",
              });
            }
          });

          self.add("transformToCircle", () => {
            // Hide text first
            if (textRef.current) {
              animate(textRef.current, {
                display: "none",
                duration: 300,
                ease: "out(3)",
              });
            }

            // Transform button to circle
            if (buttonRef.current) {
              animate(buttonRef.current, {
                width: 50,
                height: 50,
                borderRadius: "50%",
                duration: 600,
                delay: 300,
                backgroundColor: "transparent",
                borderTop: "2px solid black",
                borderRight: "1px solid black",
                borderLeft: "1px solid black",
                borderBottom: "none",
                borderColor: "black",
                borderStyle: "solid",
                ease: "out(3)",
              });
            }

            // Show icon
            if (iconRef.current) {
              animate(iconRef.current, {
                display: "initial",
                scale: 1,
                duration: 400,
                delay: 500,
                ease: "out(3)",
              });
            }
          });

          self.add("transformToButton", () => {
            // Hide icon
            if (iconRef.current) {
              animate(iconRef.current, {
                display: "none",
                scale: 0,
                duration: 300,
                ease: "out(3)",
              });
            }

            // Transform back to original button state
            if (buttonRef.current) {
              animate(buttonRef.current, {
                width: originalStyles.current.width,
                height: originalStyles.current.height,
                borderRadius: originalStyles.current.borderRadius,
                backgroundColor: originalStyles.current.backgroundColor ||,
                border: originalStyles.current.border,
                transform: originalStyles.current.transform,
                duration: 600,
                delay: 200,
                ease: "out(3)",
              });
            }

            // Show text
            if (textRef.current) {
              animate(textRef.current, {
                display: "initial",
                duration: 400,
                delay: 500,
                ease: "out(3)",
              });
            }
          });
        }
      });
    }

    return () => {
      if (scope.current) {
        scope.current.revert();
      }
    };
  }, [buttonRef, scope, backgroundColor]);

  const handleClick = () => {
    if (scope.current && scope.current.methods) {
      if (!isTransformed) {
        scope.current.methods.transformToCircle();
        scope.current.methods.rotateButton();
        setIsTransformed(true);

        // Auto transform back after 3 seconds TODO: Should be triggered when the action is done with aprop
        setTimeout(() => {
          if (scope.current && scope.current.methods) {
            scope.current.methods.transformToButton();
            setIsTransformed(false);
            if (rotateAnimation.current) {
              rotateAnimation.current.pause();
              rotateAnimation.current.reset();
            }
          }
        }, 2000);
      }
    }
    if (onClick) {
      onClick();
    }
  };

  return (
    <button
      ref={buttonRef}
      onClick={handleClick}
      type="button"
      className={["kino-button", `kino-button--${size}`, mode].join(" ")}
      style={{ backgroundColor }}
      {...props}
    >
      <span ref={textRef} style={{ display: "initial" }}>
        {label}
      </span>
      <div ref={iconRef} style={{ position: "absolute", display: "none", transform: "scale(0)" }}>
        <DownloadIcon />
      </div>
    </button>
  );
};
